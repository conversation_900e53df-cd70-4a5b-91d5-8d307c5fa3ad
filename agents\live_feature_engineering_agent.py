#!/usr/bin/env python3
"""
🔧 Live Feature Engineering Agent
Calculates comprehensive technical indicators and market condition features for live stock selection

Features:
- Technical indicators: Volatility, RSI, MACD, Bollinger Bands, EMAs, Stochastic, Volume, Momentum
- Market condition features: Trend strength, Volatility regime, Market correlation
- Target variable: 5-day forward return percentage
- Robust handling of missing data and edge cases
- Integration with existing indicator engine
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import yaml
import json

# Import existing utilities
from utils.indicator_engine import IndicatorEngine
from utils.advanced_indicators import AdvancedIndicators
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class FeatureEngineering:
    """Feature engineering results for a stock"""
    symbol: str
    features: Dict[str, float]
    target_return: Optional[float]
    market_conditions: Dict[str, Any]
    feature_count: int
    is_valid: bool
    quality_score: float

class LiveFeatureEngineeringAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for calculating technical indicators and features for live stock selection
    """
    
    def __init__(self, event_bus: Any, config: Any, session_id: str): # Modified constructor
        super().__init__("LiveFeatureEngineeringAgent", event_bus, config, session_id) # Call super constructor
        self.feature_config = self.config['feature_engineering']
        self.tech_indicators = self.feature_config['technical_indicators']
        self.market_conditions = self.feature_config['market_conditions']
        self.target_config = self.feature_config['target_variable']
        
        # Initialize indicator engine
        self.indicator_engine: Optional[IndicatorEngine] = None
        self.advanced_indicators: Optional[AdvancedIndicators] = None
        
        # Feature storage
        self.calculated_features: Dict[str, FeatureEngineering] = {}
        
    async def initialize(self) -> bool:
        """Initialize the agent with required components"""
        self.log_info("🔧 Initializing Live Feature Engineering Agent...")
        try:
            self.indicator_engine = IndicatorEngine()
            self.advanced_indicators = AdvancedIndicators()
            self.initialized = True
            self.log_info("✓ Live Feature Engineering Agent initialized")
            return True
        except Exception as e:
            self.log_error(f"Failed to initialize Live Feature Engineering Agent: {e}")
            self.initialized = False
            return False
            
    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live Feature Engineering Agent started. Ready to calculate features on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live Feature Engineering Agent...")
        self.running = False
        self.log_info("🧹 Live Feature Engineering Agent stopped.")
            
    async def calculate_features_for_stocks(self, stock_data: Dict[str, pl.DataFrame]) -> Dict[str, FeatureEngineering]:
        """
        Calculate features for multiple stocks
        
        Args:
            stock_data: Dictionary mapping symbols to their OHLCV data
            
        Returns:
            Dictionary mapping symbols to their feature engineering results
        """
        self.log_info(f"🔧 Starting feature engineering for {len(stock_data)} stocks...")
        self.update_activity()
        
        results = {}
        successful_calculations = 0
        failed_calculations = 0
        
        for symbol, data in stock_data.items():
            try:
                self.log_debug(f"Calculating features for {symbol}")
                
                # Calculate features for this stock
                features = await self._calculate_stock_features(symbol, data)
                
                if features and features.is_valid:
                    results[symbol] = features
                    self.calculated_features[symbol] = features
                    successful_calculations += 1
                    self.log_debug(f"✓ Features calculated for {symbol}")
                else:
                    self.log_warning(f"⚠️ Feature calculation failed for {symbol}")
                    failed_calculations += 1
                    
            except Exception as e:
                self.log_error(f"❌ Error calculating features for {symbol}: {e}")
                failed_calculations += 1
                continue
                
        self.log_info(f"🔧 Feature engineering complete: {successful_calculations} successful, {failed_calculations} failed")
        return results
        
    async def _calculate_stock_features(self, symbol: str, data: pl.DataFrame) -> Optional[FeatureEngineering]:
        """Calculate all features for a single stock"""
        try:
            if data.is_empty() or len(data) < 20:
                self.log_warning(f"Insufficient data for {symbol}: {len(data)} records")
                return None
                
            # Sort data by timestamp
            data = data.sort("timestamp")
            
            # Calculate technical indicators
            features = {}
            
            # 1. Volatility indicators
            if self.tech_indicators['volatility']['enabled']:
                volatility_features = self._calculate_volatility_indicators(data)
                features.update(volatility_features)
                
            # 2. RSI
            if self.tech_indicators['rsi']['enabled']:
                rsi_features = self._calculate_rsi_indicators(data)
                features.update(rsi_features)
                
            # 3. MACD
            if self.tech_indicators['macd']['enabled']:
                macd_features = self._calculate_macd_indicators(data)
                features.update(macd_features)
                
            # 4. Bollinger Bands
            if self.tech_indicators['bollinger_bands']['enabled']:
                bb_features = self._calculate_bollinger_bands(data)
                features.update(bb_features)
                
            # 5. EMAs
            if self.tech_indicators['ema']['enabled']:
                ema_features = self._calculate_ema_indicators(data)
                features.update(ema_features)
                
            # 6. Stochastic Oscillator
            if self.tech_indicators['stochastic']['enabled']:
                stoch_features = self._calculate_stochastic_indicators(data)
                features.update(stoch_features)
                
            # 7. Volume indicators
            if self.tech_indicators['volume']['enabled']:
                volume_features = self._calculate_volume_indicators(data)
                features.update(volume_features)
                
            # 8. Momentum indicators
            if self.tech_indicators['momentum']['enabled']:
                momentum_features = self._calculate_momentum_indicators(data)
                features.update(momentum_features)
                
            # Calculate market condition features
            market_conditions = self._calculate_market_conditions(data)
            
            # Calculate target variable (5-day forward return)
            target_return = self._calculate_target_return(data)
            
            # Validate features
            valid_features = {k: v for k, v in features.items() if v is not None and not np.isnan(v)}
            
            # Calculate quality score
            quality_score = len(valid_features) / len(features) if features else 0
            is_valid = quality_score >= 0.7 and target_return is not None
            
            return FeatureEngineering(
                symbol=symbol,
                features=valid_features,
                target_return=target_return,
                market_conditions=market_conditions,
                feature_count=len(valid_features),
                is_valid=is_valid,
                quality_score=quality_score
            )
            
        except Exception as e:
            self.log_error(f"Feature calculation failed for {symbol}: {e}")
            return None
            
    def _calculate_volatility_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate volatility-based indicators"""
        try:
            features = {}
            config = self.tech_indicators['volatility']
            window = config['rolling_window']
            
            # Calculate returns
            returns = data['close'].pct_change()
            
            # Standard deviation volatility
            if 'std' in config['methods']:
                volatility_std = returns.rolling_std(window_size=window).last()
                features['volatility_std'] = volatility_std
                
            # ATR (Average True Range)
            if 'atr' in config['methods']:
                high_low = data['high'] - data['low']
                high_close = (data['high'] - data['close'].shift(1)).abs()
                low_close = (data['low'] - data['close'].shift(1)).abs()
                
                true_range = pl.concat([
                    high_low.to_frame("tr"),
                    high_close.to_frame("tr"),
                    low_close.to_frame("tr")
                ], how="horizontal").max_horizontal()
                
                atr = true_range.rolling_mean(window_size=window).last()
                features['atr'] = atr
                
            # Garman-Klass volatility
            if 'garman_klass' in config['methods']:
                gk_vol = (
                    0.5 * (data['high'] / data['low']).log().pow(2) -
                    (2 * np.log(2) - 1) * (data['close'] / data['open']).log().pow(2)
                ).rolling_mean(window_size=window).last()
                features['garman_klass_volatility'] = gk_vol
                
            return features
            
        except Exception as e:
            self.log_error(f"Volatility calculation failed: {e}")
            return {}
            
    def _calculate_rsi_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate RSI indicators"""
        try:
            features = {}
            config = self.tech_indicators['rsi']
            
            for period in config['periods']:
                rsi = self._calculate_rsi(data['close'], period)
                features[f'rsi_{period}'] = rsi
                
                # RSI-based features
                features[f'rsi_{period}_overbought'] = 1.0 if rsi > config['overbought'] else 0.0
                features[f'rsi_{period}_oversold'] = 1.0 if rsi < config['oversold'] else 0.0
                
            return features
            
        except Exception as e:
            self.log_error(f"RSI calculation failed: {e}")
            return {}
            
    def _calculate_rsi(self, prices: pl.Series, period: int) -> float:
        """Calculate RSI for given period"""
        try:
            delta = prices.diff()
            gain = delta.clip_lower(0).rolling_mean(window_size=period)
            loss = (-delta.clip_upper(0)).rolling_mean(window_size=period)
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.last()
            
        except Exception as e:
            self.log_error(f"RSI calculation error: {e}")
            return np.nan
            
    def _calculate_macd_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate MACD indicators"""
        try:
            features = {}
            config = self.tech_indicators['macd']
            
            # Calculate EMAs
            ema_fast = data['close'].ewm_mean(alpha=2/(config['fast_period']+1))
            ema_slow = data['close'].ewm_mean(alpha=2/(config['slow_period']+1))
            
            # MACD line
            macd_line = ema_fast - ema_slow
            
            # Signal line
            signal_line = macd_line.ewm_mean(alpha=2/(config['signal_period']+1))
            
            # Histogram
            histogram = macd_line - signal_line
            
            features['macd'] = macd_line.last()
            features['macd_signal'] = signal_line.last()
            features['macd_histogram'] = histogram.last()
            
            # MACD crossover signals
            features['macd_bullish_crossover'] = 1.0 if macd_line.last() > signal_line.last() else 0.0
            features['macd_bearish_crossover'] = 1.0 if macd_line.last() < signal_line.last() else 0.0
            
            return features
            
        except Exception as e:
            self.log_error(f"MACD calculation failed: {e}")
            return {}
            
    def _calculate_bollinger_bands(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate Bollinger Bands indicators"""
        try:
            features = {}
            config = self.tech_indicators['bollinger_bands']
            
            # Calculate moving average and standard deviation
            sma = data['close'].rolling_mean(window_size=config['period'])
            std = data['close'].rolling_std(window_size=config['period'])
            
            # Bollinger Bands
            bb_upper = sma + (config['std_dev'] * std)
            bb_lower = sma - (config['std_dev'] * std)
            bb_middle = sma
            
            current_price = data['close'].last()
            
            features['bb_upper'] = bb_upper.last()
            features['bb_middle'] = bb_middle.last()
            features['bb_lower'] = bb_lower.last()
            
            # Bollinger Band width
            bb_width = (bb_upper - bb_lower) / bb_middle
            features['bb_width'] = bb_width.last()
            
            # %B (Bollinger Band percentage)
            if config['calculate_percent_b']:
                percent_b = (current_price - bb_lower.last()) / (bb_upper.last() - bb_lower.last())
                features['bb_percent_b'] = percent_b
                
            # Position relative to bands
            features['bb_above_upper'] = 1.0 if current_price > bb_upper.last() else 0.0
            features['bb_below_lower'] = 1.0 if current_price < bb_lower.last() else 0.0
            
            return features
            
        except Exception as e:
            self.log_error(f"Bollinger Bands calculation failed: {e}")
            return {}
            
    def _calculate_ema_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate EMA indicators"""
        try:
            features = {}
            config = self.tech_indicators['ema']
            
            current_price = data['close'].last()
            
            for period in config['periods']:
                ema = data['close'].ewm_mean(alpha=2/(period+1))
                features[f'ema_{period}'] = ema.last()
                
                # Price relative to EMA
                features[f'price_above_ema_{period}'] = 1.0 if current_price > ema.last() else 0.0
                
            # EMA crossovers (if multiple periods)
            if len(config['periods']) >= 2:
                periods = sorted(config['periods'])
                for i in range(len(periods)-1):
                    fast_period = periods[i]
                    slow_period = periods[i+1]
                    
                    ema_fast = data['close'].ewm_mean(alpha=2/(fast_period+1))
                    ema_slow = data['close'].ewm_mean(alpha=2/(slow_period+1))
                    
                    features[f'ema_{fast_period}_above_ema_{slow_period}'] = 1.0 if ema_fast.last() > ema_slow.last() else 0.0
                    
            return features
            
        except Exception as e:
            self.log_error(f"EMA calculation failed: {e}")
            return {}
            
    def _calculate_stochastic_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate Stochastic Oscillator indicators"""
        try:
            features = {}
            config = self.tech_indicators['stochastic']
            
            # Calculate %K
            lowest_low = data['low'].rolling_min(window_size=config['k_period'])
            highest_high = data['high'].rolling_max(window_size=config['k_period'])
            
            k_percent = 100 * (data['close'] - lowest_low) / (highest_high - lowest_low)
            
            # Smooth %K
            k_smooth = k_percent.rolling_mean(window_size=config['smooth_k'])
            
            # Calculate %D
            d_percent = k_smooth.rolling_mean(window_size=config['d_period'])
            
            features['stoch_k'] = k_smooth.last()
            features['stoch_d'] = d_percent.last()
            
            # Stochastic signals
            features['stoch_overbought'] = 1.0 if k_smooth.last() > 80 else 0.0
            features['stoch_oversold'] = 1.0 if k_smooth.last() < 20 else 0.0
            features['stoch_k_above_d'] = 1.0 if k_smooth.last() > d_percent.last() else 0.0
            
            return features
            
        except Exception as e:
            self.log_error(f"Stochastic calculation failed: {e}")
            return {}

    def _calculate_volume_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate volume-based indicators"""
        try:
            features = {}
            config = self.tech_indicators['volume']

            # Volume SMA
            volume_sma = data['volume'].rolling_mean(window_size=config['sma_period'])
            features['volume_sma'] = volume_sma.last()

            # Volume ratio (current volume / average volume)
            current_volume = data['volume'].last()
            features['volume_ratio'] = current_volume / volume_sma.last() if volume_sma.last() > 0 else 1.0

            # Volume trend
            volume_trend = data['volume'].rolling_mean(window_size=config['volume_ratio_period'])
            features['volume_trend'] = volume_trend.last()

            # High volume signal
            features['high_volume'] = 1.0 if current_volume > 1.5 * volume_sma.last() else 0.0

            # VWAP (Volume Weighted Average Price)
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            vwap = (typical_price * data['volume']).cumsum() / data['volume'].cumsum()
            features['vwap'] = vwap.last()

            # Price relative to VWAP
            features['price_above_vwap'] = 1.0 if data['close'].last() > vwap.last() else 0.0

            return features

        except Exception as e:
            self.log_error(f"Volume calculation failed: {e}")
            return {}

    def _calculate_momentum_indicators(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate momentum indicators"""
        try:
            features = {}
            config = self.tech_indicators['momentum']

            # Rate of Change (ROC)
            roc_period = config['roc_period']
            roc = ((data['close'] / data['close'].shift(roc_period)) - 1) * 100
            features['roc'] = roc.last()

            # Williams %R
            williams_period = config['williams_r_period']
            highest_high = data['high'].rolling_max(window_size=williams_period)
            lowest_low = data['low'].rolling_min(window_size=williams_period)

            williams_r = -100 * (highest_high - data['close']) / (highest_high - lowest_low)
            features['williams_r'] = williams_r.last()

            # Momentum (price change over period)
            momentum = data['close'] - data['close'].shift(10)
            features['momentum_10'] = momentum.last()

            # Price velocity (rate of price change)
            price_velocity = data['close'].pct_change().rolling_mean(window_size=5)
            features['price_velocity'] = price_velocity.last()

            return features

        except Exception as e:
            self.log_error(f"Momentum calculation failed: {e}")
            return {}

    def _calculate_market_conditions(self, data: pl.DataFrame) -> Dict[str, Any]:
        """Calculate market condition features"""
        try:
            conditions = {}

            # Trend strength
            if self.market_conditions['trend_strength']['enabled']:
                trend_strength = self._calculate_trend_strength(data)
                conditions['trend_strength'] = trend_strength

            # Volatility regime
            if self.market_conditions['volatility_regime']['enabled']:
                volatility_regime = self._calculate_volatility_regime(data)
                conditions['volatility_regime'] = volatility_regime

            # Market correlation (placeholder - would need benchmark data)
            if self.market_conditions['market_correlation']['enabled']:
                # For now, set to neutral
                conditions['market_correlation'] = 0.0

            return conditions

        except Exception as e:
            self.log_error(f"Market conditions calculation failed: {e}")
            return {}

    def _calculate_trend_strength(self, data: pl.DataFrame) -> Dict[str, float]:
        """Calculate trend strength indicators"""
        try:
            config = self.market_conditions['trend_strength']
            lookback = config['lookback_period']

            trend_features = {}

            # Linear regression slope
            if 'linear_regression_slope' in config['methods']:
                prices = data['close'].tail(lookback).to_numpy()
                x = np.arange(len(prices))
                slope = np.polyfit(x, prices, 1)[0]
                trend_features['linear_regression_slope'] = slope

            # ADX (Average Directional Index) - simplified
            if 'adx' in config['methods']:
                high_diff = data['high'].diff()
                low_diff = data['low'].diff()

                plus_dm = pl.when(high_diff > low_diff).then(high_diff).otherwise(0).clip_lower(0)
                minus_dm = pl.when(low_diff > high_diff).then(low_diff).otherwise(0).clip_lower(0)

                # True Range
                tr1 = data['high'] - data['low']
                tr2 = (data['high'] - data['close'].shift(1)).abs()
                tr3 = (data['low'] - data['close'].shift(1)).abs()

                true_range = pl.concat([tr1.to_frame("tr"), tr2.to_frame("tr"), tr3.to_frame("tr")], how="horizontal").max_horizontal()

                # Smoothed values
                plus_di = 100 * (plus_dm.rolling_mean(window_size=14) / true_range.rolling_mean(window_size=14))
                minus_di = 100 * (minus_dm.rolling_mean(window_size=14) / true_range.rolling_mean(window_size=14))

                dx = 100 * (plus_di - minus_di).abs() / (plus_di + minus_di)
                adx = dx.rolling_mean(window_size=14)

                trend_features['adx'] = adx.last()

            return trend_features

        except Exception as e:
            self.log_error(f"Trend strength calculation failed: {e}")
            return {}

    def _calculate_volatility_regime(self, data: pl.DataFrame) -> str:
        """Determine volatility regime"""
        try:
            config = self.market_conditions['volatility_regime']
            lookback = config['lookback_period']

            # Calculate recent volatility
            returns = data['close'].pct_change()
            recent_vol = returns.tail(lookback).std()

            if recent_vol > config['high_vol_threshold']:
                return "high_volatility"
            elif recent_vol < config['low_vol_threshold']:
                return "low_volatility"
            else:
                return "normal_volatility"

        except Exception as e:
            self.log_error(f"Volatility regime calculation failed: {e}")
            return "unknown"

    def _calculate_target_return(self, data: pl.DataFrame) -> Optional[float]:
        """Calculate 5-day forward return target"""
        try:
            forward_days = self.target_config['forward_return_days']
            return_type = self.target_config['return_type']
            outlier_cap = self.target_config['outlier_cap']

            if len(data) < forward_days + 1:
                self.log_warning("Insufficient data for forward return calculation")
                return None

            # Get current and future price
            current_price = data['close'].tail(forward_days + 1).head(1)[0]
            future_price = data['close'].last()

            # Calculate return
            if return_type == "percentage":
                forward_return = (future_price - current_price) / current_price
            elif return_type == "log":
                forward_return = np.log(future_price / current_price)
            else:
                raise ValueError(f"Unknown return type: {return_type}")

            # Apply outlier capping
            forward_return = max(-outlier_cap, min(outlier_cap, forward_return))

            return forward_return

        except Exception as e:
            self.log_error(f"Target return calculation failed: {e}")
            return None

    def get_feature_summary(self) -> Dict[str, Any]:
        """Get summary of calculated features"""
        if not self.calculated_features:
            return {}

        total_stocks = len(self.calculated_features)
        valid_stocks = sum(1 for f in self.calculated_features.values() if f.is_valid)
        avg_feature_count = np.mean([f.feature_count for f in self.calculated_features.values()])
        avg_quality_score = np.mean([f.quality_score for f in self.calculated_features.values()])

        return {
            'total_stocks_processed': total_stocks,
            'valid_stocks': valid_stocks,
            'success_rate': valid_stocks / total_stocks if total_stocks > 0 else 0,
            'average_feature_count': avg_feature_count,
            'average_quality_score': avg_quality_score,
            'feature_names': list(next(iter(self.calculated_features.values())).features.keys()) if self.calculated_features else []
        }

    def get_calculated_features(self) -> Dict[str, FeatureEngineering]:
        """Get all calculated features"""
        return self.calculated_features.copy()

# Example usage and testing
async def main():
    """Example usage of Live Feature Engineering Agent"""
    try:
        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        dummy_config = {
            'feature_engineering': {
                'technical_indicators': {
                    'volatility': {'enabled': True, 'rolling_window': 20, 'methods': ['std', 'atr', 'garman_klass']},
                    'rsi': {'enabled': True, 'periods': [14], 'overbought': 70, 'oversold': 30},
                    'macd': {'enabled': True, 'fast_period': 12, 'slow_period': 26, 'signal_period': 9},
                    'bollinger_bands': {'enabled': True, 'period': 20, 'std_dev': 2, 'calculate_percent_b': True},
                    'ema': {'enabled': True, 'periods': [5, 20, 50]},
                    'stochastic': {'enabled': True, 'k_period': 14, 'smooth_k': 3, 'd_period': 3},
                    'volume': {'enabled': True, 'sma_period': 20, 'volume_ratio_period': 5},
                    'momentum': {'enabled': True, 'roc_period': 10, 'williams_r_period': 14}
                },
                'market_conditions': {
                    'trend_strength': {'enabled': True, 'lookback_period': 20, 'methods': ['linear_regression_slope', 'adx']},
                    'volatility_regime': {'enabled': True, 'lookback_period': 20, 'high_vol_threshold': 0.02, 'low_vol_threshold': 0.005},
                    'market_correlation': {'enabled': True}
                },
                'target_variable': {
                    'forward_return_days': 5,
                    'return_type': 'percentage',
                    'outlier_cap': 0.1
                }
            }
        }
        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveFeatureEngineeringAgent(event_bus, dummy_config, session_id)
        await agent.initialize()
        await agent.start()

        # Create sample data for testing
        sample_data = {}
        for symbol in ["RELIANCE", "TCS", "INFY"]:
            # Generate sample OHLCV data
            dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
            np.random.seed(42)

            prices = 100 + np.cumsum(np.random.randn(30) * 0.02)
            volumes = np.random.randint(1000, 10000, 30)

            df_data = {
                'timestamp': dates,
                'open': prices * (1 + np.random.randn(30) * 0.001),
                'high': prices * (1 + np.abs(np.random.randn(30)) * 0.01),
                'low': prices * (1 - np.abs(np.random.randn(30)) * 0.01),
                'close': prices,
                'volume': volumes
            }

            sample_data[symbol] = pl.DataFrame(df_data)

        # Calculate features
        features = await agent.calculate_features_for_stocks(sample_data)

        # Print results
        print(f"\n🔧 Calculated features for {len(features)} stocks:")
        for symbol, feature_eng in features.items():
            print(f"  {symbol}: {feature_eng.feature_count} features, Quality: {feature_eng.quality_score:.2f}")
            print(f"    Target return: {feature_eng.target_return:.4f}" if feature_eng.target_return else "    No target return")

        # Print summary
        summary = agent.get_feature_summary()
        print(f"\n📊 Feature Engineering Summary:")
        print(f"  Success rate: {summary['success_rate']*100:.1f}%")
        print(f"  Average features per stock: {summary['average_feature_count']:.1f}")
        print(f"  Average quality score: {summary['average_quality_score']:.2f}")

        await agent.stop()

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
