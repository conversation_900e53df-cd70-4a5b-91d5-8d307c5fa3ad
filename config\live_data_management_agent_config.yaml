data_management:
  historical_days: 25
  data_directory: "data/live"
  file_format: "parquet"
  compression: "snappy"
  api_rate_limit:
    requests_per_minute: 60
    requests_per_second: 1
  stock_universe:
    max_stocks_to_process: 100
    exclude_symbols: []
    include_only: []
  data_quality:
    min_data_points: 1000
    max_missing_percentage: 5
    outlier_detection: true
    outlier_method: "iqr"
    outlier_threshold: 1.5

smartapi:
  api_key: "${SMARTAPI_API_KEY}"
  username: "${SMARTAPI_USERNAME}"
  password: "${SMARTAPI_PASSWORD}"
  totp_token: "${SMARTAPI_TOTP_TOKEN}"
