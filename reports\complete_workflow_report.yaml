insights:
  recommendations:
  - Focus on strategies with predicted Sharpe ratio > 1.0
  - Avoid strategies with predicted drawdown > 15%
  - Consider ensemble approach with top 3-5 strategies
  - Monitor model performance and retrain monthly
  risk_warnings: []
  top_strategies: []
model_performance:
  drawdown_prediction:
    feature_importance:
      lightgbm:
      - feature: roi_drawdown_ratio
        importance: 493
      - feature: avg_total_pnl
        importance: 452
      - feature: max_max_drawdown
        importance: 350
      - feature: min_max_drawdown
        importance: 313
      - feature: avg_sharpe_ratio
        importance: 266
      - feature: std_max_drawdown
        importance: 251
      - feature: min_expectancy
        importance: 249
      - feature: std_total_trades
        importance: 246
      - feature: avg_expectancy
        importance: 241
      - feature: std_winning_trades
        importance: 240
      - feature: std_profit_factor
        importance: 233
      - feature: min_sharpe_ratio
        importance: 225
      - feature: min_profit_factor
        importance: 224
      - feature: min_accuracy
        importance: 223
      - feature: max_profit_factor
        importance: 222
      - feature: avg_profit_factor
        importance: 217
      - feature: avg_accuracy
        importance: 216
      - feature: std_sharpe_ratio
        importance: 215
      - feature: min_total_pnl
        importance: 212
      - feature: std_accuracy
        importance: 212
      - feature: std_total_pnl
        importance: 205
      - feature: max_accuracy
        importance: 191
      - feature: max_total_pnl
        importance: 190
      - feature: std_expectancy
        importance: 179
      - feature: avg_total_trades
        importance: 174
      - feature: avg_winning_trades
        importance: 171
      - feature: max_expectancy
        importance: 169
      - feature: max_sharpe_ratio
        importance: 149
      - feature: avg_roi
        importance: 99
      - feature: sharpe_consistency
        importance: 93
      - feature: consistency_score
        importance: 78
      - feature: min_roi
        importance: 70
      - feature: trades_per_period
        importance: 41
      - feature: max_roi
        importance: 37
      - feature: std_roi
        importance: 10
      - feature: walk_forward_steps
        importance: 2
    lightgbm:
      best_params:
        bagging_fraction: 0.952166132560851
        feature_fraction: 0.9686340558803725
        learning_rate: 0.15556053182540092
        max_depth: 7
        n_estimators: 424
        num_leaves: 53
      mae: 0.43192400400012404
      mape: 0.06136289673073553
      mse: 0.3462750058909585
      r2_score: 0.9651764240579207
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - &id001 !!python/object/apply:numpy.dtype
        args:
        - f8
        - false
        - true
        state: !!python/tuple
        - 3
        - <
        - null
        - null
        - null
        - -1
        - -1
        - 0
      - !!binary |
        BiiX85fU4j8=
      test_score: 0.9651764240579207
      train_score: 0.9999981722139122
  ensemble: {}
  profit_factor_prediction:
    feature_importance:
      lightgbm:
      - feature: avg_accuracy
        importance: 822
      - feature: max_profit_factor
        importance: 690
      - feature: min_profit_factor
        importance: 655
      - feature: std_profit_factor
        importance: 530
      - feature: avg_sharpe_ratio
        importance: 490
      - feature: avg_expectancy
        importance: 349
      - feature: max_accuracy
        importance: 330
      - feature: roi_drawdown_ratio
        importance: 320
      - feature: min_accuracy
        importance: 301
      - feature: min_sharpe_ratio
        importance: 290
      - feature: sharpe_consistency
        importance: 274
      - feature: std_total_trades
        importance: 259
      - feature: max_max_drawdown
        importance: 257
      - feature: std_max_drawdown
        importance: 245
      - feature: min_expectancy
        importance: 235
      - feature: std_expectancy
        importance: 234
      - feature: std_sharpe_ratio
        importance: 232
      - feature: std_accuracy
        importance: 229
      - feature: max_sharpe_ratio
        importance: 214
      - feature: max_expectancy
        importance: 211
      - feature: std_winning_trades
        importance: 209
      - feature: avg_winning_trades
        importance: 173
      - feature: avg_max_drawdown
        importance: 173
      - feature: max_total_pnl
        importance: 171
      - feature: avg_total_pnl
        importance: 169
      - feature: avg_total_trades
        importance: 164
      - feature: min_total_pnl
        importance: 155
      - feature: consistency_score
        importance: 141
      - feature: min_max_drawdown
        importance: 136
      - feature: std_total_pnl
        importance: 123
      - feature: trades_per_period
        importance: 117
      - feature: avg_roi
        importance: 108
      - feature: max_roi
        importance: 93
      - feature: min_roi
        importance: 92
      - feature: std_roi
        importance: 89
      - feature: walk_forward_steps
        importance: 1
    lightgbm:
      best_params:
        bagging_fraction: 0.6328929388128114
        feature_fraction: 0.5088069967055135
        learning_rate: 0.025996664267042245
        max_depth: 5
        n_estimators: 834
        num_leaves: 88
      mae: 0.030660905420674497
      mape: 0.03218994644837354
      mse: 0.0014814286701071008
      r2_score: 0.939876854063817
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        OrmCq9+0oz8=
      test_score: 0.939876854063817
      train_score: 0.992932596315479
  profitability_classification:
    feature_importance:
      lightgbm:
      - feature: avg_total_trades
        importance: 0
      - feature: max_expectancy
        importance: 0
      - feature: std_profit_factor
        importance: 0
      - feature: min_profit_factor
        importance: 0
      - feature: max_profit_factor
        importance: 0
      - feature: avg_max_drawdown
        importance: 0
      - feature: std_max_drawdown
        importance: 0
      - feature: min_max_drawdown
        importance: 0
      - feature: max_max_drawdown
        importance: 0
      - feature: avg_sharpe_ratio
        importance: 0
      - feature: std_sharpe_ratio
        importance: 0
      - feature: min_sharpe_ratio
        importance: 0
      - feature: max_sharpe_ratio
        importance: 0
      - feature: walk_forward_steps
        importance: 0
      - feature: consistency_score
        importance: 0
      - feature: roi_drawdown_ratio
        importance: 0
      - feature: sharpe_consistency
        importance: 0
      - feature: avg_profit_factor
        importance: 0
      - feature: min_expectancy
        importance: 0
      - feature: std_total_trades
        importance: 0
      - feature: std_expectancy
        importance: 0
      - feature: avg_winning_trades
        importance: 0
      - feature: std_winning_trades
        importance: 0
      - feature: avg_accuracy
        importance: 0
      - feature: std_accuracy
        importance: 0
      - feature: min_accuracy
        importance: 0
      - feature: max_accuracy
        importance: 0
      - feature: avg_total_pnl
        importance: 0
      - feature: std_total_pnl
        importance: 0
      - feature: min_total_pnl
        importance: 0
      - feature: max_total_pnl
        importance: 0
      - feature: avg_roi
        importance: 0
      - feature: std_roi
        importance: 0
      - feature: min_roi
        importance: 0
      - feature: max_roi
        importance: 0
      - feature: avg_expectancy
        importance: 0
      - feature: trades_per_period
        importance: 0
    lightgbm:
      accuracy: 1.0
      best_params:
        bagging_fraction: 0.8376439959849006
        feature_fraction: 0.7841939234881212
        learning_rate: 0.09703819811830569
        max_depth: 4
        n_estimators: 204
        num_leaves: 42
      f1_score: 1.0
      precision: 1.0
      recall: 1.0
      test_score: 1.0
      train_score: 1.0
  roi_prediction:
    feature_importance:
      lightgbm:
      - feature: avg_total_pnl
        importance: 1362
      - feature: roi_drawdown_ratio
        importance: 508
      - feature: avg_max_drawdown
        importance: 502
      - feature: std_max_drawdown
        importance: 322
      - feature: min_max_drawdown
        importance: 284
      - feature: min_total_pnl
        importance: 256
      - feature: max_max_drawdown
        importance: 202
      - feature: std_total_pnl
        importance: 194
      - feature: avg_expectancy
        importance: 156
      - feature: avg_winning_trades
        importance: 121
      - feature: min_sharpe_ratio
        importance: 115
      - feature: avg_total_trades
        importance: 113
      - feature: avg_profit_factor
        importance: 110
      - feature: std_total_trades
        importance: 103
      - feature: avg_accuracy
        importance: 96
      - feature: max_total_pnl
        importance: 92
      - feature: std_accuracy
        importance: 90
      - feature: std_profit_factor
        importance: 90
      - feature: max_profit_factor
        importance: 89
      - feature: min_roi
        importance: 86
      - feature: min_profit_factor
        importance: 83
      - feature: max_accuracy
        importance: 81
      - feature: std_winning_trades
        importance: 81
      - feature: avg_sharpe_ratio
        importance: 79
      - feature: min_expectancy
        importance: 76
      - feature: min_accuracy
        importance: 76
      - feature: sharpe_consistency
        importance: 69
      - feature: std_sharpe_ratio
        importance: 69
      - feature: max_sharpe_ratio
        importance: 64
      - feature: std_expectancy
        importance: 58
      - feature: max_expectancy
        importance: 57
      - feature: consistency_score
        importance: 39
      - feature: max_roi
        importance: 36
      - feature: trades_per_period
        importance: 22
      - feature: std_roi
        importance: 10
      - feature: walk_forward_steps
        importance: 4
    lightgbm:
      best_params:
        bagging_fraction: 0.6894449138656542
        feature_fraction: 0.9667228617747066
        learning_rate: 0.07779210496669794
        max_depth: 7
        n_estimators: 545
        num_leaves: 88
      mae: 0.07112549010922559
      mape: 0.03210834837643002
      mse: 0.03537975228149203
      r2_score: 0.9956481598598298
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        +8iHvX8TyD8=
      test_score: 0.9956481598598298
      train_score: 0.9998364820297376
  sharpe_ratio_prediction:
    feature_importance:
      lightgbm:
      - feature: min_sharpe_ratio
        importance: 449
      - feature: std_sharpe_ratio
        importance: 443
      - feature: roi_drawdown_ratio
        importance: 417
      - feature: avg_expectancy
        importance: 349
      - feature: sharpe_consistency
        importance: 340
      - feature: avg_accuracy
        importance: 333
      - feature: consistency_score
        importance: 308
      - feature: max_sharpe_ratio
        importance: 292
      - feature: avg_profit_factor
        importance: 290
      - feature: std_profit_factor
        importance: 283
      - feature: std_winning_trades
        importance: 280
      - feature: min_expectancy
        importance: 275
      - feature: std_total_trades
        importance: 265
      - feature: max_expectancy
        importance: 263
      - feature: max_max_drawdown
        importance: 248
      - feature: max_accuracy
        importance: 247
      - feature: min_profit_factor
        importance: 244
      - feature: avg_winning_trades
        importance: 242
      - feature: std_accuracy
        importance: 241
      - feature: std_total_pnl
        importance: 225
      - feature: min_accuracy
        importance: 223
      - feature: std_expectancy
        importance: 223
      - feature: max_total_pnl
        importance: 219
      - feature: max_profit_factor
        importance: 213
      - feature: std_max_drawdown
        importance: 206
      - feature: avg_total_pnl
        importance: 202
      - feature: avg_max_drawdown
        importance: 200
      - feature: avg_total_trades
        importance: 197
      - feature: min_max_drawdown
        importance: 185
      - feature: min_total_pnl
        importance: 179
      - feature: trades_per_period
        importance: 143
      - feature: min_roi
        importance: 119
      - feature: max_roi
        importance: 93
      - feature: avg_roi
        importance: 90
      - feature: std_roi
        importance: 88
      - feature: walk_forward_steps
        importance: 15
    lightgbm:
      best_params:
        bagging_fraction: 0.6423166590864551
        feature_fraction: 0.6125544676569983
        learning_rate: 0.08907252720860119
        max_depth: 8
        n_estimators: 592
        num_leaves: 96
      mae: 0.1297869878409839
      mape: 0.11667991914751988
      mse: 0.030686897001520025
      r2_score: 0.981221994964586
      rmse: !!python/object/apply:numpy.core.multiarray.scalar
      - *id001
      - !!binary |
        WGwfKjFsxj8=
      test_score: 0.981221994964586
      train_score: 0.9998851774026922
next_steps:
- Deploy top-performing strategies in paper trading
- Set up automated model retraining pipeline
- Implement real-time prediction serving
- Monitor strategy performance and model drift
predictions:
  drawdown_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  profit_factor_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  profitability_classification:
    error: X has 15 features, but StandardScaler is expecting 37 features as input.
  roi_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
  sharpe_ratio_prediction:
    error: X has 15 features, but StandardScaler is expecting 36 features as input.
timestamp: '2025-08-04T10:56:40.349148'
workflow_summary:
  backtesting_completed: true
  ml_training_completed: true
  predictions_generated: true
