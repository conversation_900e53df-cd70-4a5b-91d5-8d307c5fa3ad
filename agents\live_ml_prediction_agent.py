#!/usr/bin/env python3
"""
🧠 Live ML Prediction Agent
Loads trained models and generates predictions for live stock selection

Features:
- Loads trained models from existing ML workflow
- Predicts expected returns, risk metrics, and strategy suitability
- Generates confidence intervals for predictions
- Handles model versioning and fallback scenarios
- Integrates with enhanced model training agent
"""

import asyncio
import logging
import polars as pl
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import yaml
import json
import joblib
import pickle
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor

# Import existing ML infrastructure
from agents.enhanced_model_training_agent import EnhancedModelTrainingAgent, EnhancedTrainingConfig
from agents.base_agent import BaseAgent, AgentStatus # Import BaseAgent

logger = logging.getLogger(__name__)

@dataclass
class PredictionResult:
    """Prediction result for a stock"""
    symbol: str
    expected_return: Optional[float]
    expected_return_confidence: Optional[float]
    risk_metrics: Dict[str, float]
    strategy_suitability: Dict[str, float]
    confidence_intervals: Dict[str, Tuple[float, float]]
    model_versions: Dict[str, str]
    prediction_quality: float
    is_valid: bool

@dataclass
class ModelInfo:
    """Information about a loaded model"""
    name: str
    version: str
    path: str
    performance_score: float
    last_updated: datetime
    is_loaded: bool

class LiveMLPredictionAgent(BaseAgent): # Inherit from BaseAgent
    """
    Agent responsible for loading trained models and generating predictions for live stock selection
    """
    
    def __init__(self, event_bus: Any, config: Any, session_id: str): # Modified constructor
        super().__init__("LiveMLPredictionAgent", event_bus, config, session_id) # Call super constructor
        self.ml_config = self.config['ml_prediction']
        self.model_config = self.ml_config['model_loading']
        self.prediction_config = self.ml_config['prediction_tasks']
        
        # Model storage
        self.loaded_models: Dict[str, Any] = {}
        self.model_info: Dict[str, ModelInfo] = {}
        self.scalers: Dict[str, Any] = {}
        self.label_encoders: Dict[str, Any] = {}
        
        # Enhanced model training agent for integration
        self.enhanced_agent: Optional[EnhancedModelTrainingAgent] = None
        
        # Prediction storage
        self.predictions: Dict[str, PredictionResult] = {}
        
    async def initialize(self) -> bool:
        """Initialize the agent and load models"""
        self.log_info("🧠 Initializing Live ML Prediction Agent...")
        try:
            # Initialize enhanced model training agent for integration
            try:
                enhanced_config = EnhancedTrainingConfig()
                enhanced_config.models_dir = self.model_config['models_directory']
                self.enhanced_agent = EnhancedModelTrainingAgent(enhanced_config)
                self.log_info("✓ Enhanced model training agent initialized")
            except Exception as e:
                self.log_warning(f"Enhanced agent initialization failed: {e}")
                
            # Load trained models
            success = await self._load_trained_models()
            
            if success:
                self.initialized = True
                self.log_info("✓ Live ML Prediction Agent initialized successfully")
                return True
            else:
                self.log_error("❌ Failed to load required models")
                self.initialized = False
                return False
                
        except Exception as e:
            self.log_error(f"Failed to initialize Live ML Prediction Agent: {e}")
            self.initialized = False
            return False
            
    async def start(self):
        """Start the agent - currently no continuous operations"""
        self.log_info("Live ML Prediction Agent started. Ready to generate predictions on demand.")
        self.running = True

    async def stop(self):
        """Stop the agent and clean up resources"""
        self.log_info("Stopping Live ML Prediction Agent...")
        self.running = False
        self.log_info("🧹 Live ML Prediction Agent stopped.")
            
    async def _load_trained_models(self) -> bool:
        """Load trained models from disk"""
        try:
            models_dir = Path(self.model_config['models_directory'])
            
            if not models_dir.exists():
                self.log_error(f"Models directory not found: {models_dir}")
                return False
                
            # Determine model version to load
            model_version = self.model_config['model_version']
            if model_version == "latest":
                model_version = self._find_latest_model_version(models_dir)
                
            if not model_version:
                self.log_error("No model version found")
                return False
                
            self.log_info(f"Loading models version: {model_version}")
            
            # Load models for each prediction task
            loaded_count = 0
            
            for task_name, task_config in self.prediction_config.items():
                if not task_config.get('enabled', True):
                    continue
                    
                try:
                    success = await self._load_task_models(task_name, model_version, models_dir)
                    if success:
                        loaded_count += 1
                        self.log_info(f"✓ Loaded models for task: {task_name}")
                    else:
                        self.log_warning(f"⚠️ Failed to load models for task: {task_name}")
                        
                except Exception as e:
                    self.log_error(f"Error loading models for task {task_name}: {e}")
                    
            # Load scalers and encoders
            await self._load_preprocessing_components(model_version, models_dir)
            
            self.log_info(f"📊 Loaded models for {loaded_count} prediction tasks")
            return loaded_count > 0
            
        except Exception as e:
            self.log_error(f"Model loading failed: {e}")
            return False
            
    def _find_latest_model_version(self, models_dir: Path) -> Optional[str]:
        """Find the latest model version"""
        try:
            # Look for timestamped model files
            model_files = list(models_dir.glob("**/training_results_*.json"))
            
            if not model_files:
                self.log_warning("No training results files found")
                return None
                
            # Extract timestamps and find latest
            latest_timestamp = None
            for file_path in model_files:
                try:
                    timestamp = file_path.stem.split('_')[-1]
                    if not latest_timestamp or timestamp > latest_timestamp:
                        latest_timestamp = timestamp
                except Exception:
                    continue
                    
            return latest_timestamp
            
        except Exception as e:
            self.log_error(f"Failed to find latest model version: {e}")
            return None
            
    async def _load_task_models(self, task_name: str, version: str, models_dir: Path) -> bool:
        """Load models for a specific prediction task"""
        try:
            task_dir = models_dir / task_name
            
            if not task_dir.exists():
                self.log_warning(f"Task directory not found: {task_dir}")
                return False
                
            # Load individual models
            task_models = {}
            fallback_models = self.model_config.get('fallback_models', ['lightgbm'])
            
            # Try to load each model type
            for model_file in task_dir.glob(f"*_{version}.joblib"):
                try:
                    model_name = model_file.stem.replace(f'_{version}', '')
                    model = joblib.load(model_file)
                    task_models[model_name] = model
                    
                    # Store model info
                    self.model_info[f"{task_name}_{model_name}"] = ModelInfo(
                        name=model_name,
                        version=version,
                        path=str(model_file),
                        performance_score=0.0,  # Would load from training results
                        last_updated=datetime.fromtimestamp(model_file.stat().st_mtime),
                        is_loaded=True
                    )
                    
                    self.log_debug(f"Loaded model: {model_name} for task: {task_name}")
                    
                except Exception as e:
                    self.log_error(f"Failed to load model {model_file}: {e}")
                    
            # Ensure at least one fallback model is available
            if not task_models and fallback_models:
                self.log_warning(f"No models loaded for {task_name}, creating fallback model")
                task_models['fallback'] = RandomForestRegressor(n_estimators=50, random_state=42)
                
            if task_models:
                self.loaded_models[task_name] = task_models
                return True
            else:
                return False
                
        except Exception as e:
            self.log_error(f"Failed to load task models for {task_name}: {e}")
            return False
            
    async def _load_preprocessing_components(self, version: str, models_dir: Path):
        """Load scalers and label encoders"""
        try:
            # Load scalers
            scalers_file = models_dir / f"scalers_{version}.joblib"
            if scalers_file.exists():
                self.scalers = joblib.load(scalers_file)
                self.log_debug("✓ Scalers loaded")
                
            # Load label encoders
            encoders_file = models_dir / f"label_encoders_{version}.joblib"
            if encoders_file.exists():
                self.label_encoders = joblib.load(encoders_file)
                self.log_debug("✓ Label encoders loaded")
                
        except Exception as e:
            self.log_error(f"Failed to load preprocessing components: {e}")
            
    async def generate_predictions(self, features_data: Dict[str, Dict[str, float]]) -> Dict[str, PredictionResult]:
        """
        Generate predictions for multiple stocks
        
        Args:
            features_data: Dictionary mapping symbols to their features
            
        Returns:
            Dictionary mapping symbols to their prediction results
        """
        self.log_info(f"🔮 Generating predictions for {len(features_data)} stocks...")
        self.update_activity()
        
        results = {}
        successful_predictions = 0
        failed_predictions = 0
        
        for symbol, features in features_data.items():
            try:
                self.log_debug(f"Generating predictions for {symbol}")
                
                # Generate predictions for this stock
                prediction = await self._generate_stock_predictions(symbol, features)
                
                if prediction and prediction.is_valid:
                    results[symbol] = prediction
                    self.predictions[symbol] = prediction
                    successful_predictions += 1
                    self.log_debug(f"✓ Predictions generated for {symbol}")
                else:
                    self.log_warning(f"⚠️ Prediction generation failed for {symbol}")
                    failed_predictions += 1
                    
            except Exception as e:
                self.log_error(f"❌ Error generating predictions for {symbol}: {e}")
                failed_predictions += 1
                continue
                
        self.log_info(f"🔮 Prediction generation complete: {successful_predictions} successful, {failed_predictions} failed")
        return results
        
    async def _generate_stock_predictions(self, symbol: str, features: Dict[str, float]) -> Optional[PredictionResult]:
        """Generate predictions for a single stock"""
        try:
            # Prepare feature vector
            feature_vector = self._prepare_feature_vector(features)
            
            if feature_vector is None:
                self.log_warning(f"Failed to prepare feature vector for {symbol}")
                return None
                
            # Generate predictions for each task
            expected_return = None
            expected_return_confidence = None
            risk_metrics = {}
            strategy_suitability = {}
            confidence_intervals = {}
            model_versions = {}
            
            # Expected return prediction
            if 'expected_return' in self.prediction_config and self.prediction_config['expected_return']['enabled']:
                return_pred, return_conf = await self._predict_expected_return(feature_vector)
                expected_return = return_pred
                expected_return_confidence = return_conf
                model_versions['expected_return'] = 'loaded'
                
            # Risk metrics prediction
            if 'risk_metrics' in self.prediction_config and self.prediction_config['risk_metrics']['enabled']:
                risk_pred = await self._predict_risk_metrics(feature_vector)
                risk_metrics = risk_pred
                model_versions['risk_metrics'] = 'loaded'
                
            # Strategy suitability prediction
            if 'strategy_suitability' in self.prediction_config and self.prediction_config['strategy_suitability']['enabled']:
                strategy_pred = await self._predict_strategy_suitability(feature_vector)
                strategy_suitability = strategy_pred
                model_versions['strategy_suitability'] = 'loaded'
                
            # Generate confidence intervals if enabled
            if self.ml_config['confidence_intervals']['enabled']:
                confidence_intervals = await self._generate_confidence_intervals(feature_vector)
                
            # Calculate prediction quality
            prediction_quality = self._calculate_prediction_quality(
                expected_return, expected_return_confidence, risk_metrics, strategy_suitability
            )
            
            # Determine if prediction is valid
            is_valid = (
                expected_return is not None and
                prediction_quality >= 0.5 and
                len(risk_metrics) > 0
            )
            
            return PredictionResult(
                symbol=symbol,
                expected_return=expected_return,
                expected_return_confidence=expected_return_confidence,
                risk_metrics=risk_metrics,
                strategy_suitability=strategy_suitability,
                confidence_intervals=confidence_intervals,
                model_versions=model_versions,
                prediction_quality=prediction_quality,
                is_valid=is_valid
            )
            
        except Exception as e:
            self.log_error(f"Prediction generation failed for {symbol}: {e}")
            return None

    def _prepare_feature_vector(self, features: Dict[str, float]) -> Optional[np.ndarray]:
        """Prepare feature vector for model prediction"""
        try:
            # Convert features to numpy array
            feature_names = sorted(features.keys())
            feature_values = [features[name] for name in feature_names]

            # Handle missing values
            feature_values = [0.0 if v is None or np.isnan(v) else v for v in feature_values]

            feature_vector = np.array(feature_values).reshape(1, -1)

            # Apply scaling if available
            if self.scalers:
                # Use the first available scaler (assuming consistent scaling)
                scaler_name = next(iter(self.scalers.keys()))
                scaler = self.scalers[scaler_name]
                feature_vector = scaler.transform(feature_vector)

            return feature_vector

        except Exception as e:
            self.log_error(f"Feature vector preparation failed: {e}")
            return None

    async def _predict_expected_return(self, feature_vector: np.ndarray) -> Tuple[Optional[float], Optional[float]]:
        """Predict expected return"""
        try:
            task_name = 'expected_return'

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return None, None

            models = self.loaded_models[task_name]
            predictions = []
            confidences = []

            # Get predictions from all available models
            for model_name, model in models.items():
                try:
                    if hasattr(model, 'predict'):
                        pred = model.predict(feature_vector)[0]
                        predictions.append(pred)

                        # Calculate confidence (simplified)
                        if hasattr(model, 'predict_proba'):
                            proba = model.predict_proba(feature_vector)[0]
                            confidence = np.max(proba)
                        else:
                            confidence = 0.8  # Default confidence for regression
                        confidences.append(confidence)

                except Exception as e:
                    self.log_error(f"Prediction failed for model {model_name}: {e}")

            if predictions:
                # Ensemble prediction (average)
                expected_return = np.mean(predictions)
                avg_confidence = np.mean(confidences)
                return expected_return, avg_confidence
            else:
                return None, None

        except Exception as e:
            self.log_error(f"Expected return prediction failed: {e}")
            return None, None

    async def _predict_risk_metrics(self, feature_vector: np.ndarray) -> Dict[str, float]:
        """Predict risk metrics"""
        try:
            task_name = 'risk_metrics'
            risk_metrics = {}

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return risk_metrics

            models = self.loaded_models[task_name]
            targets = self.prediction_config[task_name].get('targets', ['expected_drawdown', 'volatility'])

            # Predict each risk metric
            for target in targets:
                predictions = []

                for model_name, model in models.items():
                    try:
                        if hasattr(model, 'predict'):
                            pred = model.predict(feature_vector)[0]
                            predictions.append(pred)
                    except Exception as e:
                        self.log_error(f"Risk prediction failed for {model_name}: {e}")

                if predictions:
                    risk_metrics[target] = np.mean(predictions)

            return risk_metrics

        except Exception as e:
            self.log_error(f"Risk metrics prediction failed: {e}")
            return {}

    async def _predict_strategy_suitability(self, feature_vector: np.ndarray) -> Dict[str, float]:
        """Predict strategy suitability scores"""
        try:
            task_name = 'strategy_suitability'
            strategy_scores = {}

            if task_name not in self.loaded_models:
                self.log_warning(f"No models loaded for {task_name}")
                return strategy_scores

            models = self.loaded_models[task_name]
            strategy_types = self.prediction_config[task_name].get('strategy_types',
                                                                  ['momentum', 'mean_reversion', 'breakout', 'trend_following'])

            # Get predictions from models
            for model_name, model in models.items():
                try:
                    if hasattr(model, 'predict_proba'):
                        # Classification model
                        probabilities = model.predict_proba(feature_vector)[0]

                        # Map probabilities to strategy types
                        for i, strategy in enumerate(strategy_types[:len(probabilities)]):
                            if strategy not in strategy_scores:
                                strategy_scores[strategy] = []
                            strategy_scores[strategy].append(probabilities[i])

                    elif hasattr(model, 'predict'):
                        # Regression model - assume single strategy score
                        score = model.predict(feature_vector)[0]
                        if strategy_types:
                            strategy_scores[strategy_types[0]] = [score]

                except Exception as e:
                    self.log_error(f"Strategy suitability prediction failed for {model_name}: {e}")

            # Average scores across models
            final_scores = {}
            for strategy, scores in strategy_scores.items():
                if scores:
                    final_scores[strategy] = np.mean(scores)

            return final_scores

        except Exception as e:
            self.log_error(f"Strategy suitability prediction failed: {e}")
            return {}

    async def _generate_confidence_intervals(self, feature_vector: np.ndarray) -> Dict[str, Tuple[float, float]]:
        """Generate confidence intervals for predictions"""
        try:
            confidence_intervals = {}

            if not self.ml_config['confidence_intervals']['enabled']:
                return confidence_intervals

            confidence_level = self.ml_config['confidence_intervals']['confidence_level']
            method = self.ml_config['confidence_intervals']['method']

            # For now, implement simple confidence intervals
            # In a full implementation, this would use bootstrap or quantile regression

            for task_name in self.loaded_models.keys():
                if task_name == 'expected_return':
                    # Simple confidence interval based on model uncertainty
                    lower_bound = -0.05  # -5%
                    upper_bound = 0.05   # +5%
                    confidence_intervals[task_name] = (lower_bound, upper_bound)

            return confidence_intervals

        except Exception as e:
            self.log_error(f"Confidence interval generation failed: {e}")
            return {}

    def _calculate_prediction_quality(self, expected_return: Optional[float],
                                    expected_return_confidence: Optional[float],
                                    risk_metrics: Dict[str, float],
                                    strategy_suitability: Dict[str, float]) -> float:
        """Calculate overall prediction quality score"""
        try:
            quality_components = []

            # Return prediction quality
            if expected_return is not None and expected_return_confidence is not None:
                quality_components.append(expected_return_confidence)
            else:
                quality_components.append(0.0)

            # Risk metrics quality
            if risk_metrics:
                risk_quality = min(1.0, len(risk_metrics) / 2)  # Expect at least 2 risk metrics
                quality_components.append(risk_quality)
            else:
                quality_components.append(0.0)

            # Strategy suitability quality
            if strategy_suitability:
                strategy_quality = min(1.0, len(strategy_suitability) / 3)  # Expect at least 3 strategies
                quality_components.append(strategy_quality)
            else:
                quality_components.append(0.0)

            # Overall quality (average of components)
            return np.mean(quality_components) if quality_components else 0.0

        except Exception as e:
            self.log_error(f"Prediction quality calculation failed: {e}")
            return 0.0

    def get_model_info(self) -> Dict[str, ModelInfo]:
        """Get information about loaded models"""
        return self.model_info.copy()

    def get_predictions(self) -> Dict[str, PredictionResult]:
        """Get all generated predictions"""
        return self.predictions.copy()

    def get_prediction_summary(self) -> Dict[str, Any]:
        """Get summary of prediction results"""
        if not self.predictions:
            return {}

        total_predictions = len(self.predictions)
        valid_predictions = sum(1 for p in self.predictions.values() if p.is_valid)
        avg_quality = np.mean([p.prediction_quality for p in self.predictions.values()])

        return {
            'total_predictions': total_predictions,
            'valid_predictions': valid_predictions,
            'success_rate': valid_predictions / total_predictions if total_predictions > 0 else 0,
            'average_quality': avg_quality,
            'loaded_models': list(self.loaded_models.keys())
        }

# Example usage and testing
async def main():
    """Example usage of Live ML Prediction Agent"""
    try:
        # For testing, create a dummy config and event bus
        class DummyEventBus:
            async def publish(self, event_type: str, payload: Dict):
                print(f"Event Published: {event_type} - {payload}")

        dummy_config = {
            'ml_prediction': {
                'model_loading': {
                    'models_directory': 'data/models/enhanced', # Assuming this path exists for models
                    'model_version': 'latest',
                    'fallback_models': ['random_forest']
                },
                'prediction_tasks': {
                    'expected_return': {'enabled': True},
                    'risk_metrics': {'enabled': True, 'targets': ['expected_drawdown', 'volatility']},
                    'strategy_suitability': {'enabled': True, 'strategy_types': ['momentum', 'mean_reversion', 'breakout', 'trend_following']}
                },
                'confidence_intervals': {
                    'enabled': True,
                    'confidence_level': 0.95,
                    'method': 'simple'
                }
            }
        }
        event_bus = DummyEventBus()
        session_id = "test_session_123"

        agent = LiveMLPredictionAgent(event_bus, dummy_config, session_id)
        await agent.initialize()
        await agent.start()

        # Create sample features for testing
        sample_features = {
            "RELIANCE": {
                "volatility_std": 0.02,
                "rsi_14": 65.0,
                "macd": 0.5,
                "bb_percent_b": 0.7,
                "ema_20": 2500.0,
                "volume_ratio": 1.2,
                "roc": 2.5
            },
            "TCS": {
                "volatility_std": 0.015,
                "rsi_14": 45.0,
                "macd": -0.2,
                "bb_percent_b": 0.3,
                "ema_20": 3800.0,
                "volume_ratio": 0.9,
                "roc": -1.0
            }
        }

        # Generate predictions
        predictions = await agent.generate_predictions(sample_features)

        # Print results
        print(f"\n🔮 Generated predictions for {len(predictions)} stocks:")
        for symbol, prediction in predictions.items():
            print(f"  {symbol}:")
            print(f"    Expected return: {prediction.expected_return:.4f}" if prediction.expected_return else "    No expected return")
            print(f"    Risk metrics: {prediction.risk_metrics}")
            print(f"    Strategy suitability: {prediction.strategy_suitability}")
            print(f"    Quality: {prediction.prediction_quality:.2f}")

        # Print summary
        summary = agent.get_prediction_summary()
        print(f"\n📊 Prediction Summary:")
        print(f"  Success rate: {summary['success_rate']*100:.1f}%")
        print(f"  Average quality: {summary['average_quality']:.2f}")

        await agent.stop()

    except Exception as e:
        logger.error(f"Example failed: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
